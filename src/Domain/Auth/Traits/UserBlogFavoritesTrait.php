<?php

namespace Domain\Auth\Traits;

use DomainException;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait UserBlogFavoritesTrait {

    public function blogFavorites(): BelongsToMany
    {
        return $this->belongsToMany(config('domain.blog'), 'blog_favorites_ref', 'user_id', 'blog_id');
    }

    public function hasBlogInFavorites($id): bool
    {
        return $this->blogFavorites()->where('id', 'like', $id)->exists();
    }

    public function addBlogToFavorites($blogId): void
    {
        if ($this->hasBlogInFavorites($blogId)) {
            throw new DomainException(config('constants.blog_already_in_favorites'));
        }
        // если отсутствует, то прикрепи блог
        $this->blogFavorites()->attach($blogId);
    }

    public function removeBlogFromFavorites($blogIds): void
    {
        if (!$this->hasBlogInFavorites($blogIds)) {
            throw new DomainException(config('constants.blog_already_out_favorites'));
        }
        // если присутствует, то открепи блог
        $this->blogFavorites()->detach($blogIds);
    }
}
