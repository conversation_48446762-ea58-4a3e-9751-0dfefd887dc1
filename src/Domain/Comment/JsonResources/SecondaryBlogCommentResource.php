<?php
namespace Domain\Comment\JsonResources;

use Domain\Comment\Models\BlogComment;
use Illuminate\Http\Resources\Json\JsonResource;

class SecondaryBlogCommentResource extends JsonResource
{
    public function toArray($request)
    {
        /** @var BlogComment $this */
        $parent = $this->getParentId();
        $reply = $this->getReplyId();
        return [
            'id' => $this->id,
            'message' => $this->comment,
            'createdAt' => $this->created_at,
            'isDeleted' => false,
            'author' => new CommentAuthorResource($this->user),
            'blog' => [
                'id' => $this->blog?->id,
                'title' => $this->blog?->title
            ],
            'replyComment' => $reply ? [
                'id' => $reply->id,
                'author' => new CommentAuthorResource($reply?->user),
            ] : null,
            'totalReplies' => $parent?->id ?
                $parent->children()->count() :
                 $this->children()->count(),
            'parent' => $parent ? [
                'id' => $parent->id,
                'author' => new CommentAuthorResource($parent?->user),
            ] : null,
            'total' => $this->blog?->comments()->count(),
            'hasInComplaints' => authUser()?->hasInBlogCommentComplaints($this->id),
        ];
    }
}
