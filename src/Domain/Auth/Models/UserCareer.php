<?php

namespace Domain\Auth\Models;

use Illuminate\Support\Carbon;
use Database\Factories\UserCareerFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Domain\Auth\QueryBuilders\UserCareerQueryBuilder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $company
 * @property string $position
 * @property string $duty
 * @property string $id
 * @property Carbon $start_date_at
 * @property Carbon $last_date_at
 * @property BelongsTo $user
 * @method static UserCareer|UserCareerQueryBuilder query()
 *
*/
class UserCareer extends Model {

    use HasFactory;

    protected $table = 'carrers';
    protected $fillable = ['company', 'position', 'duty', 'user_id', 'last_date_at', 'start_date_at'];
    protected $casts = [
        'start_date_at' => 'datetime',
        'last_date_at' => 'datetime',
    ];
    protected $hidden = [
        'updated_at', 'user_id'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function newEloquentBuilder($query): Builder
    {
        return new UserCareerQueryBuilder($query);
    }

    protected static function newFactory(): UserCareerFactory
    {
        return UserCareerFactory::new();
    }
}
