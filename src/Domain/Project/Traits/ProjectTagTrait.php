<?php

namespace Domain\Project\Traits;

use Domain\Project\Models\ProjectTag;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait ProjectTagTrait {
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(ProjectTag::class, 'tags_ref', 'project_id', 'tag_id');
    }

    public function hasInTags($tagId): bool
    {
        // Существуют ли какие-либо строки для текущего запроса.
        return $this->tags()->where('id', $tagId)->exists();
    }

    public function addToTags($tagId): void
    {
        if ($this->hasInTags($tagId)) {
            throw new \DomainException('This tag is already added to tags.');
        }
        $this->tags()->attach($tagId);
    }

    public function removeFromTags($tagId): void
    {
        if (!$this->hasInTags($tagId)) {
            throw new \DomainException('This tag is already removed from tags.');
        }
        $this->tags()->detach($tagId);
    }
}
