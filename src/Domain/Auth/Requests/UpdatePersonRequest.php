<?php

namespace Domain\Auth\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePersonRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }


    public function rules()
    {
        return [
            'lastname' => [],
            'socials' => [],
            'city' => [],
            'region' => [],
            'country' => [],
            'avatar' => [],
            'rolesInProject', [],
            'firstname' => ['required', 'string', 'max:255', 'min:2'],
            'desiredPosition' => ['required', 'string', 'max:255', 'min:2'],
        ];
    }

    protected function prepareForValidation() :void
    {
        $this->merge([
            'socials' => jsonDecode($this['socials']),
        ]);
    }
}
