<?php

namespace Domain\Chat\DTOs;

use Illuminate\Http\Request;
use Support\Traits\Makeable;
use Domain\Chat\Enums\ChatCredentialsEnum;

class ChatDTO {

    use Makeable;

    public function __construct(
        public ?string $id = "",
        public ?string $value = "",
        public ?string $message = "",
        public ?string $author_id = "",
        public ?string $project_id = "",
        public ?string $companion_id = "",
    ){}

    public static function fromRequest(Request $request): ChatDTO {

        return self::make(...$request->only([
            ChatCredentialsEnum::ID->value,
            ChatCredentialsEnum::VALUE->value,
            ChatCredentialsEnum::MESSAGE->value,
            ChatCredentialsEnum::AUTHOR_ID->value,
            ChatCredentialsEnum::PROJECT_ID->value,
            ChatCredentialsEnum::COMPANION_ID->value,
        ]));
    }
}
