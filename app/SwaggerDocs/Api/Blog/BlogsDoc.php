<?php

namespace App\SwaggerDocs\Api\Blog;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class BlogsDoc extends \OpenApi\Annotations\Get
{
    public function __construct() {
        parent::__construct([
            'path' => '/blogs',
            'tags' => ['Blogs'],
            'summary' => "Project/User blogs",
            'operationId' => 'blogs',
            "description" => "Here you can get all blogs of users and projects",
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Blogs of users and projects",
                    content: paginationJsonContentDoc(blogProperties())
                ),
            ],
        ]);

    }
}
