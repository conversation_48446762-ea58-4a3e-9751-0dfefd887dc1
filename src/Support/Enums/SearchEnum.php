<?php

namespace Support\Enums;

enum SearchEnum: string {
    case VALUE = 'value';
    case VALUES = 'values';
    case SORT_BY = 'sortBy';
    case SEARCH_BY = 'searchBy';
    case COUNTRY = 'country';
    case REGION = 'region';
    case CITY = 'city';
    case POSITION = 'position';
    case ROLE = 'role';
    case COUNTRY_ID = 'country_id';
    case REGION_ID = 'region_id';
    case CITY_ID = 'city_id';
}
