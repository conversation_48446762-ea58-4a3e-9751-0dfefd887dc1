<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Support\Enums\SearchCredentialsEnum as Search;

class SearchRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'values' => ['array']
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'value' => $this['value'] ?? "",
            'values' => $this['values'] ?? [],
            'city_id' => $this['cityId'] ?? "",
            'region_id' => $this['regionId'] ?? "",
            'country_id' => $this['countryId'] ?? "",
            'sortBy' => $this['projectsSortBy'] ?? $this['usersSortBy'] ?? "",
            'searchBy' => $this['projectsSearchBy'] ?? $this['usersSearchBy'] ?? [Search::SEARCH_BY_TAGS->value]
        ]);
    }

    protected function passedValidation()
    {
        $this->merge([
            'value' => $this['value'] ?? "",
            'values' => array_filter(array_unique([...$this['values'], $this['value']])),

        ]);
    }

    public function messages()
    {
        return [];
    }
}
