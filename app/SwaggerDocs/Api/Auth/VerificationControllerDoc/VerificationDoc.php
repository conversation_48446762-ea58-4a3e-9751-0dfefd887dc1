<?php

namespace App\SwaggerDocs\Api\Auth\VerificationControllerDoc;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class VerificationDoc extends \OpenApi\Annotations\Get
{
    public function __construct() {
        parent::__construct([
            'path' => '/verification/{token}',
            'tags' => ['Auth'],
            'summary' => "Email verification",
            'operationId' => 'verification',
            "description" => "Here you can verify your email",
            "parameters" => [
                new OA\Parameter(name: "token", description: "Verification token", required: true, in: "path",
                    schema: new OA\Schema(type: "string")
                )
            ],
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Successfully verified",
                    content: new OA\JsonContent(
                        properties: [
                            new OA\Property(property: "data", type: "object", properties: [
                                new OA\Property(property: "message", type: "string"),
                                new OA\Property(property: "verified", type: "boolean")
                            ]),
                        ]
                    )
                ),
                forbiddenResponseDoc()
            ],
        ]);

    }
}
