<?php
namespace Domain\Auth\Requests;

use Domain\Auth\Rules\ValidatePassword;
use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    public function rules() : array
    {
        return [
            'firstname' => ['required', 'string', 'max:255', 'min:2'],
            'password' => ['required', new ValidatePassword()],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255', 'unique:users'],
        ];
    }
}
