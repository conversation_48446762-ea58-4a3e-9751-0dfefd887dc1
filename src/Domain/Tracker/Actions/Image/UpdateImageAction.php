<?php

namespace Domain\Tracker\Actions\Image;

use Exception;
use Domain\Tracker\DTOs\TrackerDTO;
use Domain\Tracker\Models\TrackerImage;
use Domain\Tracker\Models\TrackerTask;
use Domain\Tracker\Contracts\Images\UpdateImageInterface;

class UpdateImageAction implements UpdateImageInterface {

    public function __invoke(TrackerDTO $dto): void {

            /** @var TrackerTask $task */
            $task = TrackerTask::query()->where("id", $dto->id)->first();
            $images = $task->images()->get();

            $collectionName = TrackerImage::TRACKER_IMAGES . '' . $dto->id . '';
            $requestedIds = [];

            try {
                foreach($dto->images as $file) {

                    if  (is_string($file)) {
                        $imageObj = jsonDecode($file);
                        $requestedIds[] = $imageObj['id'];
                    } else {
                        $image = TrackerImage::query()->make([
                            'url' => ""
                        ]);
                        $image->task()->associate($dto->id);
                        $image->saveOrFail();

                        if (isset($file)) {
                            $image->addMedia($file)->toMediaCollection($collectionName);
                            $url = $image->getFirstMediaUrl($collectionName);
                            $image->update([
                                'url' => $url
                            ]);
                        }
                    }
                }
            } catch(Exception $ex) {}

            try {
                foreach($images as $model) {
                    if (!in_array($model->id, $requestedIds)) {
                        $media = $model->getMedia($collectionName)->where('model_id', $model->id)->first();
                        $model?->deleteMedia($media->id);
                        $model?->delete();
                    }
                };
            } catch(Exception $ex) {}
    }

}
