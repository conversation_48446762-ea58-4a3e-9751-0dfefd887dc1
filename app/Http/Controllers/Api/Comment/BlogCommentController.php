<?php

namespace App\Http\Controllers\Api\Comment;

use Domain\Blog\Models\Blog;
use Illuminate\Http\JsonResponse;
use App\Helpers\Response\Response;
use Domain\Comment\DTOs\CommentDTO;
use App\Http\Controllers\Controller;
use Domain\Comment\Models\BlogComment;
use Domain\Comment\Requests\UpdateRequest;
use Domain\Comment\Actions\DeleteCommentAction;
use Domain\Comment\Actions\UpdateCommentAction;
use Domain\Comment\Requests\CreatePrimaryCommentRequest;
use Domain\Comment\Contracts\CreatePrimaryBlogCommentInterface;
use Domain\Comment\Contracts\CreateSecondaryBlogCommentInterface;
use Domain\Comment\JsonResources\PrimaryBlogCommentListResource;
use Domain\Comment\JsonResources\SecondaryBlogCommentListResource;
use Domain\Comment\JsonResources\SecondaryBlogCommentResource;
use Domain\Comment\Requests\CreateSecondaryBlogCommentRequest;
// swagger
use App\SwaggerDocs\Api\Comments\BlogComments\CommentsCountDoc;
use App\SwaggerDocs\Api\Comments\BlogComments\CreatePrimaryCommentDoc;
use App\SwaggerDocs\Api\Comments\BlogComments\CreateSecondaryCommentDoc;
use App\SwaggerDocs\Api\Comments\BlogComments\DeleteCommentDoc;
use App\SwaggerDocs\Api\Comments\BlogComments\PrimaryCommentsDoc;
use App\SwaggerDocs\Api\Comments\BlogComments\SecondaryCommentsDoc;
use App\SwaggerDocs\Api\Comments\BlogComments\UpdateCommentDoc;

class BlogCommentController extends Controller
{

    // основные коментарии
    #[PrimaryCommentsDoc]
    public function getComments(Blog $blog): JsonResponse
    {
        $comments = $blog?->comments()->noChildAndOrder()
            ->withChildren()
            ->paginate(config('constants.comments_per_page'));
        return Response::HTTP_OK(PrimaryBlogCommentListResource::collection($comments));
    }


    // ответы к коментарию
    #[SecondaryCommentsDoc]
    public function getReplies(BlogComment $comment): JsonResponse
    {
        $replies = $comment->children()->withReply()
            ->orderBy('created_at')
            ->paginate(config('constants.replies_per_page'));;
        return Response::HTTP_OK(SecondaryBlogCommentListResource::collection($replies));
    }


    // кол-во комментариев к блогу
    #[CommentsCountDoc]
    public function getCommentsCount(Blog $blog)
    {
        return Response::HTTP_OK([
            'total' => $blog->comments()->count(),
        ]);
    }


    // создать комментарий (родителя/основной)
    #[CreatePrimaryCommentDoc]
    public function createComment(CreatePrimaryCommentRequest $request, string $slug, CreatePrimaryBlogCommentInterface $action): JsonResponse
    {
        $blog = Blog::query()->withSlug($slug)->firstOrError();

        $dto = CommentDTO::fromRequest($request);
        $dto->setBlogId($blog->id);

        return Response::HTTP_CREATED($action($dto));
    }


    // обновить комментарий
    #[UpdateCommentDoc]
    public function updateComment(UpdateRequest $request, string $id, UpdateCommentAction $action): JsonResponse
    {
        $comment = BlogComment::query()->authCommentById($id, authUser()->id);
        return Response::HTTP_OK(new SecondaryBlogCommentResource($action($comment, $request['message'])));
    }


    // удалить комментарий
    #[DeleteCommentDoc]
    public function deleteComment(string $id, DeleteCommentAction $action): JsonResponse
    {
        $comment = BlogComment::query()->authCommentById($id, authUser()->id);
        return Response::HTTP_OK(new SecondaryBlogCommentResource($action($comment)));
    }


    // создать ответ на комментарий
    #[CreateSecondaryCommentDoc]
    public function createReply(CreateSecondaryBlogCommentRequest $request, string $id, CreateSecondaryBlogCommentInterface $action): JsonResponse
    {
        $dto = CommentDTO::fromRequest($request);
        return Response::HTTP_CREATED($action($dto));
    }
}
