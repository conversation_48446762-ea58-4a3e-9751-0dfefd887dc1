<?php

namespace App\SwaggerDocs\Api\Comments\BlogComments;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class UpdateCommentDoc extends \OpenApi\Annotations\Put
{
    public function __construct() {
        parent::__construct([
            'path' => '/blog-comments/{id}',
            'tags' => ['Blog comments'],
            'security' => [
                [
                    'bearerAuth' => []
                ]
            ],
            "parameters" => [
                new OA\Parameter(name: "id", description: "Comment id", required: true, in: "path",
                    schema: new OA\Schema(type: "string")
                )
            ],
            'summary' => "Update blog comment",
            'operationId' => 'update-blog-comment',
            "description" => "Here you can update blog comment",
            'requestBody' => new OA\RequestBody(required: true,
                request: "update-blog-comment",
                content: new OA\MediaType(mediaType: "application/json",
                schema: new OA\Schema(required: ["message"],
                        properties: [
                            new OA\Property(property: 'message', type: "string"),
                        ]
                ))),
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Successfully created",
                    content: new OA\JsonContent(
                        properties: []
                    )
                ),
                badRequestResponseDoc(),
                unauthorizedResponseDoc(),
                unprocessableEntityResponseDoc()
            ],
        ]);

    }
}
