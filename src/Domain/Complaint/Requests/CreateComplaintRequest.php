<?php

namespace Domain\Complaint\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateComplaintRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules()
    {
        return [
            'reason' => 'required|string'
        ];
    }

    public function messages()
    {
        return [
            'reason.string' => config('constants.transmit_incorrect_data'),
            'reason.required' => config('constants.transmit_incorrect_data'),
        ];
    }
}
