<?php

namespace App\Http\Controllers\Api\Project;

use Domain\Auth\Models\User;
use App\Events\Project\ProjectEvent;
use Domain\Project\Models\Project;
use Illuminate\Http\JsonResponse;
use App\Helpers\Response\Response;
use App\Http\Controllers\Controller;
use Domain\WebSocket\WebSocketManager;
use Domain\Project\Enums\ProjectNotifyTypeEnum as Type;
use Domain\Project\JsonResource\UpdateProjectResource;
use App\Http\Resources\Notification\ProjectNotificationResource;
// Swagger
use App\SwaggerDocs\Api\Project\Favorite\AddProjectToFavoritesDoc;
use App\SwaggerDocs\Api\Project\Favorite\DeleteProjectFromFavoritesDoc;

class FavoriteController extends Controller
{
    /** @var WebSocketManager $manager */
    private $manager;
    public function __construct() {
        $this->manager = app()->make(config('broadcasting.manager'));
    }


    // добавить проект в понравившийся
    #[AddProjectToFavoritesDoc]
    public function add(string $id): JsonResponse
    {
        $user = authUser();
        $project = Project::query()->activeById($id)->firstOrError();

        attachModel($user, $project, 'addToFavorites');

        // Events
        event(new ProjectEvent($project, $user, Type::TO_FAVORITES->value));
        // Centrifugo
        /** @var User $author */
        $author = $project->user;
        if ($author?->showLikes() && $author->id != $user->id) {
            $this->manager->notifyProject(
                $author?->id,
                new ProjectNotificationResource($project, $user, Type::TO_FAVORITES->value)
            );
        }

        return Response::HTTP_OK(new UpdateProjectResource($project));
    }


    // удалить проект из понравившихся
    #[DeleteProjectFromFavoritesDoc]
    public function remove(string $id): JsonResponse
    {
        $user = authUser();
        $project = Project::query()->activeById($id)->firstOrError();

        attachModel($user, $project, 'removeFromFavorites');

        return Response::HTTP_OK(new UpdateProjectResource($project));
    }
}
