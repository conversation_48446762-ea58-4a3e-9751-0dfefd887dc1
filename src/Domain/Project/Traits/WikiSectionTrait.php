<?php

namespace Domain\Project\Traits;

use Domain\Project\Models\ProjectWikiSection;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait WikiSectionTrait {

    public function wikiSections(): HasMany
    {
        return $this->hasMany(ProjectWikiSection::class, 'project_id', 'id');
    }

    public function groupedSections() {
        return $this->wikiSections()
            ->where('project_id', $this->id)
            ->orderByDesc('created_at')
            ->get()
            ->groupBy('parent_id');
    }

    public function projectSections() {
        return $this->wikiSections()
            ->where('project_id', $this->id)
            ->orderByDesc('created_at')
            ->get();
    }

    public function sectionById(string $id) {
        return $this->wikiSections()->where('id', 'like', $id);
    }
}
