<?php

namespace Services\AI\Listeners;

use Illuminate\Support\Facades\Cache;
use Services\AI\Events\StreamChunkReceived;

// Возможно пригодится в будущем
class StreamChunkListener
{
    public function handle(StreamChunkReceived $event)
    {
        // Получаем уникальный ключ для хранения (можно привязать к ID задачи, пользователя и т.д.)
        $key = config('ai.botHub.cacheKey') . $event->class;

        // Добавляем новый кусочек к уже существующим
        $fullText = Cache::get($key, '');
        $fullText .= $event->chunk;

        Cache::put($key, $fullText, now()->addMinutes(30)); // сохраняем в кэш на 30 минут
    }
}
