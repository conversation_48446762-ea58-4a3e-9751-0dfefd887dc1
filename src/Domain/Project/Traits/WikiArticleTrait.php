<?php

namespace Domain\Project\Traits;

use Domain\Project\Models\ProjectWikiArticle;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait WikiArticleTrait {

    public function wikiArticles(): Has<PERSON><PERSON>
    {
        return $this->hasMany(ProjectWikiArticle::class, 'project_id', 'id');
    }

    public function groupedArticles() {
        return $this->wikiArticles()
            ->where('project_id', $this->id)
            ->orderByDesc('created_at')
            ->get()
            ->groupBy('section_id');
    }

    public function articleById(string $id) {
        return $this->wikiArticles()->where('id', 'like', $id);
    }

    public function defaultArticle(): BelongsToMany
    {
        return $this->belongsToMany(ProjectWikiArticle::class, 'default_article_ref', 'project_id', 'article_id')
            ->withPivot('project_id')
            ->withPivot('article_id');
    }

    public function projectDefaultArticle(): BelongsToMany
    {
        return $this->defaultArticle()->wherePivot('project_id', 'like', $this->id);
    }

    public function hasInDefaultArticle($articleId)
    {
        return $this->defaultArticle()
            ->wherePivot('project_id', 'like', $this->id)
            ->exists();
    }

    public function addToDefaultArticle($articleId, array $attributes = []): void
    {
        if ($this->hasInDefaultArticle($articleId)) {
            $this->defaultArticle()->detach();
        }
        $this->defaultArticle()->attach($articleId, $attributes);
    }

    public function removeFromDefaultArticle(): void
    {
        $this->defaultArticle()?->detach();
    }
}
