## Деплой проекта на Ubuntu Server 18.04 LTS
1. Установить Ubuntu Server 18.04 LTS
1. Клонировать проект в папку `/var/www/html/`
    ```git clone https://gitlab.com/team2you/team2you.git -b dev```
1. Запустить скрипт для настройки установки пакетов и конфигурации рабочего окружения
    ```
    sudo bash /var/www/html/team2you/_install/standalone/provision.sh $USER
    ```
    ⚠ Установка может занять около получаса
1. Создать VirtualHost
    ```
    sudo cp /var/www/html/team2you/_install/standalone/team2.vh /etc/nginx/sites-available
    sudo ln -s /etc/nginx/sites-available/team2.vh /etc/nginx/sites-enabled/
    sudo service nginx reload
    ```
1. Установить зависимости проекта
    ```
    cd /var/www/html/team2you
    sudo composer install
    sudo npm install
    ```
1. Создать структуру БД
    `php artisan migrate`
###### Добавить запись в hosts файл основной ОС
`<server IP addr> homestead.test`

###### ⚠ После рестарта системы необходимо запустить службы php, nginx и mysql
    ```
    sudo bash /var/www/html/team2you/_install/standalone/startup.sh
 
# Установка elasticSearch
    1. Установить на свою ОС
        https://www.elastic.co/guide/en/elasticsearch/reference/current/install-elasticsearch.html 
    2. Убедиться, что в .env файле есть строка
        ELASTICSEARCH_HOSTS=127.0.0.1:9200, где 9200 порт по умолчанию (elasticSearch)    
    3. Выполнить из директории проекта
        php artisan search:init
        php artisan search:reindex    ```

# Google OAuth на локалке 
    1. Создать client_id и client_secret
        https://developers.google.com/identity/protocols/oauth2/web-server#creatingcred
    2. Добавить в .env
        GOOGLE_CLIENT_ID={your client_id}
        GOOGLE_CLIENT_SECRET={your client_secret}
        GOOGLE_REDIRECT={your_host}/auth/google,   GOOGLE_REDIRECT такой же должен быть в console.developers
            
