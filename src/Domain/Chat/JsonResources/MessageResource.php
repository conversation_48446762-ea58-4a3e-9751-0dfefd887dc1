<?php
namespace Domain\Chat\JsonResources;

use Domain\Chat\Models\AppChat;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource
{
    public function toArray($request)
    {
        /** @var AppChat $this */
        return [
            'id' => $this->id,
            'from' => $this->from,
            'to' => $this->to,
            'text' => $this->text,
            'createdAt' => $this->created_at
        ];
    }
}
