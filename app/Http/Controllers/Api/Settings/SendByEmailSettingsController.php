<?php
namespace App\Http\Controllers\Api\Settings;

use App\Helpers\Response\Response;
use App\Http\Controllers\Controller;
use Domain\Auth\Requests\UpdateSettingsRequest;
use Domain\Auth\JsonResources\Settings\SendByEmailSettings;
// Swagger
use App\SwaggerDocs\Api\Settings\UpdateEmailSettingsDoc;

class SendByEmailSettingsController extends Controller
{
    #[UpdateEmailSettingsDoc]
    public function update(UpdateSettingsRequest $request)
    {
        $key = $request['key'];
        $value = $request['value'];
        $user = authUser();
        $sendByEmailSettings = $user->emailSettings()->get()->first();
        $sendByEmailSettings->update([
            $key => $value
        ]);
        return Response::HTTP_OK(new SendByEmailSettings($sendByEmailSettings));
    }
}
