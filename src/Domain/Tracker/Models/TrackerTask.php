<?php

namespace Domain\Tracker\Models;

use Domain\Tracker\Enums\ImportanceEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Domain\Tracker\Traits\HasDeleteImages;
use Domain\Tracker\Traits\ContractorsTrait;
use Illuminate\Database\Eloquent\Collection;
use Domain\Tracker\Casts\HoursEstimationCast;
use Domain\Tracker\Casts\MinutesEstimationCast;
use Domain\Tracker\QueryBuilders\TaskQueryBuilder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property string $id
 * @property string $title
 * @property string $column_id
 * @property string $description
 * @property BelongsTo $column
 * @property HasMany $images
 * @property HasMany $comments
 * @property number $hours_estimation
 * @property number $minutes_estimation
 * @property ImportanceEnum $importance
 * @property BelongsToMany|Collection $contractors
 * @method static TrackerTask|TaskQueryBuilder query()
 */
class TrackerTask extends Model {

    use ContractorsTrait, HasDeleteImages;
    protected $table = 'tracker_tasks';
    protected $fillable = ['title', 'column_id', 'description', 'importance', 'hours_estimation', 'minutes_estimation'];
    protected $hidden = ['created_at', 'updated_at'];
    protected $guarded = [];
    protected $casts = [
        'importance' => ImportanceEnum::class,
        'minutes_estimation' => MinutesEstimationCast::class,
        'hours_estimation' => HoursEstimationCast::class
    ];

    public function column(): BelongsTo
    {
        return $this->belongsTo(TrackerColumn::class, 'column_id', 'id');
    }

    public function images(): HasMany
    {
        return $this->hasMany(TrackerImage::class, 'task_id', 'id');
    }

    public function comments(): HasMany
    {
        return $this->hasMany(TrackerTaskComment::class, 'task_id', 'id');
    }

    public function newEloquentBuilder($query): Builder
    {
        return new TaskQueryBuilder($query);
    }

}
