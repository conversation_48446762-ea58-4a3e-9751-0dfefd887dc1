<?php

namespace App\Http\Controllers\Api\Wiki;

use Exception;
use Domain\Project\Models\Project;
use App\Helpers\Response\Response;
use App\Http\Controllers\Controller;
// Swagger
use App\SwaggerDocs\Api\Wiki\GetCombineDoc;

class CombineController extends Controller
{
    #[GetCombineDoc]
    public function getCombine(string $project_id) {
        /** @var Project $project */
        $project = Project::query()->projectWithSubscriber($project_id, authUser()->id)->firstOrError();

        $sections = $project->groupedSections();
        $articles = $project->groupedArticles();

        $_articles = array_keys($articles->all());

        foreach($_articles as $article) {
            try {
               $sections[$article] = array_merge($sections[$article]->all(), $articles[$article]->all());
            }
            catch(Exception $e) {
                $sections[$article] = $articles[$article];
            }
        }
        return Response::HTTP_OK(['data' => $sections]);
    }
}
