<?php

namespace App\SwaggerDocs\Api\Blog;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class BlogDraftsOrUserDoc extends \OpenApi\Annotations\Get
{
    public function __construct() {
        parent::__construct([
            'path' => '/users/{user}/drafts',
            'tags' => ['Blogs'],
            'security' => [
                [
                    'bearerAuth' => []
                ]
            ],
            'summary' => "User blog drafts",
            'operationId' => 'user-blog-drafts',
            "description" => "Here you can get all blog drafts of user",
            "parameters" => [
                new OA\Parameter(name: "user", description: "User id", required: true, in: "path",
                    schema: new OA\Schema(type: "string")
                )
            ],
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Blog drafts of user",
                    content: blogBelongsToListJsonContentDoc(paginationJsonContentDoc(blogProperties()))
                ),
                badRequestResponseDoc(),
                unauthorizedResponseDoc()
            ],
        ]);
    }
}
