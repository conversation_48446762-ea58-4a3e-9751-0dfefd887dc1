<?php

namespace Domain\Auth\Actions\AuthActions;

use DomainException;
use Domain\Auth\Models\User;
use Domain\Auth\DTOs\CredentialDTO;
use Illuminate\Support\Facades\Auth;
use Illuminate\Auth\AuthenticationException;
use Domain\Auth\Contracts\RefreshTokenInterface;
use Domain\Auth\JsonResources\LoginSuccessResource;
use Illuminate\Http\Resources\Json\JsonResource;

class RefreshTokenAction implements RefreshTokenInterface {

    public function __invoke(CredentialDTO $dto): JsonResource
    {
        // в заголовке accessToken, в теле refresh token id
        /** @var User $user */
        $user = Auth::guard('api')->user();

        $accessTokenResult = $user?->token();
        if ($accessTokenResult?->name === 'refreshToken') {
            throw new AuthenticationException();
        }
        if ($accessTokenResult?->expires_at?->diffInMinutes(now()) <= config('constants.access_token_expires_in')) {
            throw new AuthenticationException();
        }

        $refreshTokenId = $dto->token;
        if (!$refreshTokenId) {
            throw new DomainException(config('constants.transmit_incorrect_data'));
        }
        $refreshTokenResult = $user?->tokens()
            ->where('id', 'like', $refreshTokenId)
            ->where('name', 'like', 'refreshToken')->first();

        // если разные пользователи
        if (!$refreshTokenResult || !$accessTokenResult || $refreshTokenResult?->user_id !== $accessTokenResult?->user_id) {
            throw new AuthenticationException();
        }

         // если истек
        if ($refreshTokenResult?->expires_at->diffInDays(now()) > config('constants.refresh_token_expires_in')) {
            throw new AuthenticationException();
        }

        $refreshTokenResult->delete();
        $accessTokenResult->delete();

        // создаем новую пару
        $accessToken = createAccessToken($user);
        $refreshToken = createRefreshToken($user);

        return new LoginSuccessResource($user, $accessToken, $refreshToken);
    }
}
