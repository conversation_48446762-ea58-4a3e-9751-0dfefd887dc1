<?php

namespace Domain\Auth\Actions\MediaActions;

use Domain\Auth\DTOs\MediaDTO;
use Illuminate\Support\Facades\DB;
use Domain\Auth\Models\UserImage;
use Domain\Auth\Contracts\UploadImageInterface;

class UploadImageAction implements UploadImageInterface {

    public function __invoke(MediaDTO $dto): string|null {

        $user = authUser();
        return DB::transaction(function () use ($dto, $user) {
            $image = UserImage::query()->make([
                'url' => null
            ]);
            $image->user()->associate($user);
            $image->save();

            $collectionName = UserImage::USER_IMAGES . '' . $user->id . '';

            if (isset($dto->image)) {
                $image->addMediaFromBase64($dto->image)->toMediaCollection($collectionName);
            }
            $url = $image->getMedia($collectionName)->first()->getUrl();
            $image->update([
                'url' => $url
            ]);
            return $url;
        });
    }
}
