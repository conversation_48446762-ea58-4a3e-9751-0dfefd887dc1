<?php

namespace Domain\Project\Traits;

use Domain\Project\Models\Project;
use Domain\Project\Enums\ProjectStatusEnum as Status;

trait HasSlug {

    protected static function bootHasSlug(): void {
        static::creating(function (Project $model) {
            $model->makeSlug($model);
        });
        static::updating(function (Project $model) {
            $model->editSlug($model);
        });
    }

    protected function editSlug(Project $model): void {
        /**
         * @var Project $existing
         */
        // $model - проект обновленный, но не сохраненный в БД
        $existing = $this->getTitle($model->id);
        //или перемещаю в черновик или публикую
        if ($existing->status !== $model->status) {
            if ($model->status === Status::ACTIVE->value) {
                $this->{$this->slugColumn()} = $this->createSlug();
            } else if ($model->status === Status::MODERATION->value) {
                return;
            }
            else {
                $this->{$this->slugColumn()} = generateDraftSlug();
            }
        }

        if ($existing->title !== $model->title && !$existing->hasDraft()) {
            $this->{$this->slugColumn()} = $this->createSlug();
        }
    }

    protected function makeSlug(Project $model): void {
        if ($model->status === Status::DRAFT->value) {
            $slug = generateDraftSlug();
        } else {
            $slug = $this->createSlug();
        }
        $this->{$this->slugColumn()} = $this->{$this->slugColumn()} ?? $slug;
    }

    protected function createSlug(): string {
        return $this->slugUnique(str($this->{$this->slugFrom()})->slug()->value());
    }

    protected function slugColumn(): string {
        return "slug";
    }

    protected function slugFrom(): string {
        return "title";
    }

    private function slugUnique(string $slug): string {
        $originalSlug = $slug;
        $i = 0;

        while ($this->isSlugExists($slug)) {
            $i++;

            $slug = $originalSlug . '-' . $i;
        }

        return $slug;
    }

    private function isSlugExists(string $slug): bool {
        $query = $this->newQuery()
            ->where($this->slugColumn(), 'like', $slug)
            ->where($this->getKeyName(), '!=', $this->getKey())
            ->withoutGlobalScopes();

        return $query->exists();
    }

    private function getTitle(string $id): Project {
        $query = $this->newQuery()->withTrashed()
            ->where('id', 'like', $id);
        return $query->select(['id', 'title', 'status'])->first();
    }

}
