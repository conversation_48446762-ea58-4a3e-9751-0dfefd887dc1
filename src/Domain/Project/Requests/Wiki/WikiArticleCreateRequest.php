<?php

namespace Domain\Project\Requests\Wiki;

use Illuminate\Foundation\Http\FormRequest;

class WikiArticleCreateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'section_id' => '',
            'has_article_default' => '',
            'project_id' => 'required',
            'article_title' => "required|string",
            'article_text' => 'required|string',
        ];
    }

    protected function prepareForValidation() :void
    {
        $this->merge([
            'section_id' => $this['sectionId'] ?? '',
            'project_id' => $this['project'],
            'article_title' => $this['title'],
            'article_text' => $this['text'],
            'has_article_default' => $this['hasDefault'] ?? false
        ]);

    }

    public function messages()
    {
        return [
            'article_title.string' => config('constants.transmit_incorrect_data'),
            'article_title.required' => config('constants.transmit_incorrect_data'),
            'article_text.string' => config('constants.transmit_incorrect_data'),
            'article_text.required' => config('constants.transmit_incorrect_data'),
            'project_id.required' => config('constants.transmit_incorrect_data'),
        ];
    }
}
