<?php

namespace Domain\Comment\Models;

use Database\Factories\BlogCommentFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Domain\Comment\QueryBuilders\CommentQueryBuilder;

class BlogComment extends Model {

    use HasFactory;

    protected $with = ['user'];
    protected $table = 'blog_comments';
    protected $fillable = ['comment', 'blog_id', 'user_id'];

    // Пользователь, который оставил комментарий.
    public function user(): BelongsTo
    {
        return $this->belongsTo(config('domain.user'), 'user_id', 'id');
    }

    // Блог/Статья, для которой, был оставлен, комментарий.
    public function blog(): BelongsTo
    {
        return $this->belongsTo(config('domain.blog'), 'blog_id', 'id');
    }

    // Возвращает все комментарии, для которых этот комментарий является родителем.
    public function children(): HasMany
    {
        return $this->hasMany(BlogComment::class, 'child_id');
    }

    // Возвращает комментарий, к которому принадлежит этот комментарий.
    public function parent(): BelongsTo
    {
        return $this->belongsTo(BlogComment::class, 'child_id');
    }

    // Возвращает комментарий, на который ответили.
    public function reply(): BelongsTo
    {
        return $this->belongsTo(BlogComment::class, 'reply_id');
    }

    public function getParentId(): BlogComment|null
    {
        return $this->parent()->first();;
    }

    public function getReplyId(): BlogComment|null
    {
        return $this->reply()->first();
    }

    public function newEloquentBuilder($query): Builder
    {
        return new CommentQueryBuilder($query);
    }

    public function resolveRouteBinding($id, $field = null)
    {
        return $this->query()->where('id', 'like', $id)->firstOrError();
    }

    protected static function newFactory(): BlogCommentFactory
    {
        return BlogCommentFactory::new();
    }
}
