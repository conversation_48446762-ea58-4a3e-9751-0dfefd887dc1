<?php

namespace App\Policies;

use Domain\Auth\Models\User;
use Domain\Auth\Models\UserRole;
use Illuminate\Support\Facades\Gate;
use Domain\Auth\Models\UserPermission;
use Illuminate\Database\Eloquent\Builder;
use Domain\Auth\Enums\Permissions\Wiki\EditArticle;
use Domain\Auth\Enums\Permissions\Wiki\EditSection;
use Domain\Auth\Enums\Permissions\Team\LeaveProject;
use Domain\Auth\Enums\Permissions\Wiki\CreateArticle;
use Domain\Auth\Enums\Permissions\Wiki\CreateSection;
use Domain\Auth\Enums\Permissions\Wiki\DeleteArticle;
use Domain\Auth\Enums\Permissions\Wiki\DeleteSection;
use Domain\Auth\Enums\Permissions\Communication\CreateMessage;

class ProjectManagementPolicy
{
    // register in AuthServiceProvider
    public static function registerPermissions() {
        Gate::define('create-article', [ProjectManagementPolicy::class, 'createArticle']);
        Gate::define('delete-article', [ProjectManagementPolicy::class, 'deleteArticle']);
        Gate::define('edit-article', [ProjectManagementPolicy::class, 'editArticle']);

        Gate::define('create-section', [ProjectManagementPolicy::class, 'createSection']);
        Gate::define('delete-section', [ProjectManagementPolicy::class, 'deleteSection']);
        Gate::define('edit-section', [ProjectManagementPolicy::class, 'editSection']);

        Gate::define('leave-project', [ProjectManagementPolicy::class, 'leaveProject']);

        Gate::define('create_message', [ProjectManagementPolicy::class, 'createMessage']);
    }

    // wiki
    public function createArticle(User $user, $role_id) {
        return $this->checkPermission(CreateArticle::TITLE->value, $role_id);
    }
    public function deleteArticle(User $user, $role_id) {
        return $this->checkPermission(DeleteArticle::TITLE->value, $role_id);
    }
    public function editArticle(User $user, $role_id) {
        return $this->checkPermission(EditArticle::TITLE->value, $role_id);
    }
    public function createSection(User $user, $role_id) {
        return $this->checkPermission(CreateSection::TITLE->value, $role_id);
    }
    public function deleteSection(User $user, $role_id) {
        return $this->checkPermission(DeleteSection::TITLE->value, $role_id);
    }
    public function editSection(User $user, $role_id) {
        return $this->checkPermission(EditSection::TITLE->value, $role_id);
    }

    // team
    public function leaveProject(User $user, $role_id) {
        return $this->checkPermission(LeaveProject::TITLE->value, $role_id);
    }

    // communication
    public function createMessage(User $user, $role_id) {
        return $this->checkPermission(CreateMessage::TITLE->value, $role_id);
    }

    private function checkPermission($permission, $role_id) {
        $permission = UserPermission::query()->where('title', 'like', $permission)->first();
        return UserRole::query()->whereHas('permissions', function (Builder $query) use ($role_id, $permission) {
            $query->where('role_id', $role_id);
            $query->where('permission_id', $permission->id);
        })->exists();
    }
}
