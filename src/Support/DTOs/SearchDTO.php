<?php

namespace Support\DTOs;

use Illuminate\Http\Request;
use Support\Enums\SearchEnum;
use Support\Traits\Makeable;

class SearchDTO {

    use Makeable;

    public function __construct(
        public readonly ?string $value = "",
        public readonly ?array $values = [],
        public readonly ?string $country = "",
        public readonly ?string $region = "",
        public readonly ?string $city = "",
        public readonly ?string $position = "",
        public readonly ?string $role = "",
        public readonly array|string $searchBy = [],
        public readonly array|string $sortBy = "",
        public readonly ?string $country_id = "",
        public readonly ?string $region_id = "",
        public readonly ?string $city_id = "",
    ){}

    public static function fromRequest(Request $request): SearchDTO {

        return self::make(...$request->only([
            SearchEnum::VALUE->value,
            SearchEnum::VALUES->value,
            SearchEnum::SORT_BY->value,
            SearchEnum::SEARCH_BY->value,
            SearchEnum::COUNTRY->value,
            SearchEnum::REGION->value,
            SearchEnum::CITY->value,
            SearchEnum::ROLE->value,
            SearchEnum::POSITION->value,
            SearchEnum::COUNTRY_ID->value,
            SearchEnum::REGION_ID->value,
            SearchEnum::CITY_ID->value,
        ]));
    }


}
