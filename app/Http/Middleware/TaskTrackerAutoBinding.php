<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Routing\Route;
use Illuminate\Http\Request;
use Domain\Tracker\Models\TrackerBoard;
use Domain\Tracker\Models\TrackerColumn;
use Domain\Tracker\Models\TrackerTask;
use Symfony\Component\HttpFoundation\Response;

class TaskTrackerAutoBinding
{
    public function handle(Request $request, Closure $next): Response
    {
        $_project = "";
        $_board = "";
        $_column = "";
        $_task = "";

        // if (!authUser()?->isAdmin()) {
        //     throw new \DomainException(config('constants.something_went_wrong'));
        // }

        /** @var Route $route */
        $route = $request->route();
        if ($route->hasParameter('project')) {
            $_project = $route->parameter('project');

            $route->setParameter('project',
            authUser()?->findPivotProjectWithSubscriber($_project)
                ->withoutEagerLoads()->firstOrError()
            );
        }
        if ($route->hasParameter('board')) {
            $_board = $route->parameter('board');

            $route->setParameter('board',
            TrackerBoard::query()
                    ->where('id', 'like', $_board)
                    ->where('project_id', 'like', $_project)
                    ->firstOrError()
            );
        }
        if ($route->hasParameter('column')) {
            $_column = $route->parameter('column');

            $route->setParameter('column',
            TrackerColumn::query()
                    ->where('id', 'like', $_column)
                    ->where('board_id', 'like', $_board)
                    ->firstOrError()
            );
        }
        if ($route->hasParameter('task')) {
            $_task = $route->parameter('task');

            $route->setParameter('task',
            TrackerTask::query()
                    ->where('id', 'like', $_task)
                    ->where('column_id', 'like', $_column)
                    ->firstOrError()
            );
        }
        return $next($request);
    }
}
