<?php

namespace Domain\Project\Actions;

use Domain\Project\Models\Project;
use Domain\Project\DTOs\ProjectDTO;
use Domain\Project\Enums\ProjectStatusEnum;
use Domain\Project\Contracts\ToDraftOnlyInterface;
use Domain\Project\Enums\ProjectCredentialsEnum;

class ToDraftOnlyAction implements ToDraftOnlyInterface {

    public function __invoke(ProjectDTO $dto): Project {

        $project = Project::query()->authProject($dto->id, authUser()->id);
        $project->update([
            ProjectCredentialsEnum::STATUS->value => ProjectStatusEnum::DRAFT->value
        ]);

        return $project;
    }
}
