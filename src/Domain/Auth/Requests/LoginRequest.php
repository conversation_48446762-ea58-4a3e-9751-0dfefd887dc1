<?php
namespace Domain\Auth\Requests;

use Domain\Auth\Rules\ValidatePassword;
use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'password' => ['required', new ValidatePassword()],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255'],
        ];
    }
}
