<?php

namespace Domain\Project\DTOs;

use Support\Traits\Makeable;
use Illuminate\Http\Request;
use Domain\Residence\DTOs\CityDTO;
use Domain\Project\DTOs\ProjectDTO;
use Domain\Project\DTOs\ProjectTagDTO;
use Domain\Residence\DTOs\CountryDTO;

class ProjectFactoryDTO {

    use Makeable;

    public function __construct(
        public readonly CityDTO $cityDTO,
        public readonly ProjectTagDTO $tagsDTO,
        public readonly CountryDTO $countryDTO,
        public readonly ProjectDTO $projectDTO,
        public readonly ProjectTagDTO $teamTagsDTO,
    ){}

    public static function fromRequest(Request $request) {

        return self::make(...[
            'projectDTO' => ProjectDTO::fromRequest($request),
            'cityDTO' => CityDTO::make(...jsonDecode($request->get('city'))),
            'countryDTO' => CountryDTO::make(...jsonDecode($request->get('country'))),
            'tagsDTO' => ProjectTagDTO::make(jsonDecode($request->get('projectTags'))),
            'teamTagsDTO' => ProjectTagDTO::make(jsonDecode($request->get('requireForTeamTags')))
        ]);
    }
}
