<?php

namespace Domain\Residence\DTOs;

use Illuminate\Http\Request;
use Support\Traits\Makeable;
use Domain\Residence\Enums\ResidenceCredentialsEnum;

class CountryDTO {

    use Makeable;

    public function __construct(
        public readonly ?string $id = "",
        public readonly ?string $title = "",
        public readonly ?string $country = "",
    ){}

    public static function fromRequest(Request $request): CountryDTO {

        return self::make(...$request->only([
            ResidenceCredentialsEnum::ID->value,
            ResidenceCredentialsEnum::TITLE->value,
            ResidenceCredentialsEnum::COUNTRY->value,
        ]));
    }


}
