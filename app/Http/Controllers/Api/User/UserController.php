<?php
namespace App\Http\Controllers\Api\User;

use Domain\Auth\Models\User;
use Illuminate\Http\JsonResponse;
use App\Helpers\Response\Response;
use App\Http\Controllers\Controller;
use Domain\Auth\JsonResources\AnyUserResource;
use Domain\Auth\JsonResources\UserListResource;
// Swagger
use App\SwaggerDocs\Api\User\GetUsersDoc;
use App\SwaggerDocs\Api\User\GetAnyUserDoc;

class UserController extends Controller
{
    // активные пользователи
    #[GetUsersDoc]
    public function getUsers(): JsonResponse
    {
        return Response::HTTP_OK(UserListResource::collection(
                User::query()
                    ->users()
                    ->with('skills')
                    ->orderByDesc('ratio')
                    ->paginate(config("constants.users_per_page"))
        ));

    }


    // любой пользователь активный пользователь
    #[GetAnyUserDoc]
    public function getAnyUser(string $id): JsonResponse
    {
        return Response::HTTP_OK(new AnyUserResource(
                User::query()
                    ->users($id)
                    ->skills()
                    ->careers()
                    ->socials()
                    ->qualities()
                    ->residence()
                    ->rolesProfile()
                    ->firstOrError()
        ));
    }
}
