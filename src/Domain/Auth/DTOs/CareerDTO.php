<?php

namespace Domain\Auth\DTOs;


use Illuminate\Http\Request;
use Support\Traits\Makeable;
use Domain\Auth\Enums\CareerEnum;

class CareerDTO {

    use Makeable;

    public function __construct(
        public readonly ?string $id = "",
        public readonly ?string $duty = "",
        public readonly ?string $company = "",
        public readonly ?string $position = "",
        public readonly ?string $last_date_at = "",
        public readonly ?string $start_date_at = "",
    ){}

    public static function fromRequest(Request $request): CareerDTO {

        return self::make(...$request->only([
            CareerEnum::ID->value,
            CareerEnum::DUTY->value,
            CareerEnum::POSITION->value,
            CareerEnum::COMPANY->value,
            CareerEnum::LAST_DATE_AT->value,
            CareerEnum::START_DATE_AT->value
        ]));
    }
}
