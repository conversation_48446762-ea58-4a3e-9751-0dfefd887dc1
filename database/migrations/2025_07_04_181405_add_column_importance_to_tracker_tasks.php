<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('tracker_tasks', function (Blueprint $table) {
            $table->string('importance')->default('Empty')->after('description');
        });
    }

    public function down(): void
    {
        Schema::table('tracker_tasks', function (Blueprint $table) {
            $table->dropColumn('importance');
        });
    }
};
