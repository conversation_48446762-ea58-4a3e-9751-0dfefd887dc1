<?php

namespace App\SwaggerDocs\Api\Comments\BlogComments;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class DeleteCommentDoc extends \OpenApi\Annotations\Delete
{
    public function __construct() {
        parent::__construct([
            'path' => '/blog-comments/{id}',
            'tags' => ['Blog comments'],
            'security' => [
                [
                    'bearerAuth' => []
                ]
            ],
            "parameters" => [
                new OA\Parameter(name: "id", description: "Comment id", required: true, in: "path",
                    schema: new OA\Schema(type: "string")
                )
            ],
            'summary' => "Delete blog comment",
            'operationId' => 'delete-blog-comment',
            "description" => "Here you can delete blog comment",
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Successfully deleted",
                    content: new OA\JsonContent(
                        properties: []
                    )
                ),
                badRequestResponseDoc(),
                unauthorizedResponseDoc(),
            ],
        ]);

    }
}
