<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Services\ReCaptcha\ReCaptchaApi;
use Symfony\Component\HttpFoundation\Response;
use App\Exceptions\ReCaptcha\ReCaptchaException;

class ReCaptcha
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = ReCaptchaApi::siteverify($request['token'] ?? "");
        if ($response === true) {
            return $next($request);
        } else {
            throw new ReCaptchaException(json_encode($response));
        }
    }
}
