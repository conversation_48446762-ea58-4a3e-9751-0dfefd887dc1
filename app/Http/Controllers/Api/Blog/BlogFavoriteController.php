<?php

namespace App\Http\Controllers\Api\Blog;

use Domain\Blog\Models\Blog;
use App\Helpers\Response\Response;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Domain\Blog\JsonResources\BlogResource;
use App\SwaggerDocs\Api\Blog\BlogAddToFavoritesDoc;
use App\SwaggerDocs\Api\Blog\BlogRemoveFromFavoritesDoc;

class BlogFavoriteController extends Controller
{
    // Добавить статью/блог в понравившиеся
    #[BlogAddToFavoritesDoc]
    public function addToFavorites(string $slug = ""): JsonResponse {
        $blog = Blog::query()->favoritesCount()->withSlug($slug)->firstOrError();

        attachModel(authUser(), $blog, 'addBlogToFavorites');

        return Response::HTTP_OK(new BlogResource($blog));
    }


    // Удалить статью/блог из понравившихся
    #[BlogRemoveFromFavoritesDoc]
    public function deleteFromFavorites(string $slug): JsonResponse {
        $blog = Blog::query()->favoritesCount()->withSlug($slug)->firstOrError();

        attachModel(authUser(), $blog, 'removeBlogFromFavorites');

        return Response::HTTP_OK(new BlogResource($blog));
    }
}
