<?php

namespace Domain\Auth\Actions\AuthActions;

use DomainException;
use Domain\Auth\Models\User;
use Support\DTOs\EmailDTO;
use Domain\Auth\DTOs\CredentialDTO;
use Domain\Auth\Contracts\ResendEmailInterface;
use Domain\Auth\JsonResources\SuccessResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ResendEmailAction implements ResendEmailInterface {

    public function __invoke(CredentialDTO $dto): JsonResource
    {
        /** @var User $user */
        $user = findUserByEmail($dto->email);
        if (!$user) {
            throw new DomainException(config("constants.user_not_found"));
        }
        // раз в 5 минут можно отправлять письмо на почту и обновлять код подтверждения
        if (!$user?->hasVerifiedEmail() &&
            $user?->last_email_at->diffInMinutes(now()) >= config('constants.last_email_minutes')
        ) {
            $verification_code = sha1(time());
            $user?->update([
                'verification_code' => $verification_code,
                'last_email_at' => now()
            ]);

            sendWelcomeEmail(EmailDTO::fromUser($user));

            return new SuccessResource("");
        } else {
            throw new DomainException("Письмо с ссылкой для подтверждения почтового адреса можно отправлять раз в 5 минут.");
        }
    }
}
