<?php

namespace Services\AI;

use GuzzleHttp\Client;
use Services\AI\Api\TextGenerateApi;
use Illuminate\Support\ServiceProvider;

class BotHubServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->configureDependencyInjection();
    }

    private function configureDependencyInjection(): void
    {
        $this->app->singleton(Client::class, function (): Client {
            return new Client([
                'timeout'  => 0, // Бесконечный таймаут для стрима
            ]);
        });
        $this->app->singleton(TextGenerateApi::class, function (): TextGenerateApi {
            return new TextGenerateApi(app()->make(Client::class));
        });
    }
}
