<?php

namespace App\Notifications\Message\Mail;

use Domain\Auth\Models\User;
use Domain\Chat\Models\AppChat;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class PostedMessage extends Notification implements ShouldQueue
{
    use Queueable;

    private $message;
    private $userFrom;

    public function __construct(AppChat $message, User $userFrom)
    {
        $this->message = $message;
        $this->userFrom = $userFrom;
    }

    public function via($notifiable)
    {
        /** @var User $notifiable */
        if ($notifiable->newEmailMessage() && $notifiable->last_online_at->diffInDays(now()) !== 0) {
            return ['mail'];
        }
        return [];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Вам новое сообщение')
            ->view('emails.notification.new-message', ['user' => $this->userFrom, 'text' => $this->message->text]);
    }
}
