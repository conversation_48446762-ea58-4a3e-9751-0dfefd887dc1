<?php

namespace Database\Factories;

use Domain\Project\Models\ProjectTag;
use Domain\Project\Enums\ProjectTagStatusEnum;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProjectTagFactory extends Factory
{

    protected $model = ProjectTag::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->unique()->name,
            'status' => $this->faker->boolean ?
                ProjectTagStatusEnum::ACTIVE : ProjectTagStatusEnum::MODERATE
        ];
    }
}
