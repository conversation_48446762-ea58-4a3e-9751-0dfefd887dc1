<?php

use Domain\Residence\DTOs\CityDTO;

use function PHPUnit\Framework\assertInstanceOf;


beforeEach(function () {
    $this->json =
        "{\"id\":230,\"region\":\"impedit\",\"aria\":null,\"regionId\":21,\"city\":\"Lake Gaston\",\"title\":\"impedit, Lake Gaston\"}";
});

it('cityDTO_instance_created_from_json_with_make', function () {

    $dto = CityDTO::make(...json_decode($this->json, true));

    assertInstanceOf(CityDTO::class, $dto);
});

