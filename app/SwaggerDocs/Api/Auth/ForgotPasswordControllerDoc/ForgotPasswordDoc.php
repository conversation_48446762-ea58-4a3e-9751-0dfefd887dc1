<?php

namespace App\SwaggerDocs\Api\Auth\ForgotPasswordControllerDoc;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class ForgotPasswordDoc extends \OpenApi\Annotations\Post
{
    public function __construct() {
        parent::__construct([
            'path' => '/password/recovery',
            'tags' => ['Auth'],
            'summary' => "Password recovery",
            'operationId' => 'recovery',
            "description" => "Here you can create link to recovery your password",
            'requestBody' => new OA\RequestBody(required: true,
                request: "recovery",
                content: new OA\MediaType(mediaType: "application/json",
                schema: new OA\Schema(required: ["email"],
                        properties: [
                            new OA\Property(property: 'email', description: "User email", type: "string"),
                        ]
                ))),
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_CREATED,
                    description: "Successfully created",
                    content: new OA\JsonContent(
                        properties: [
                            new OA\Property(property: "message", type: "string")
                        ]
                    )
                ),
                badRequestResponseDoc(),
                unprocessableEntityResponseDoc()
            ],
        ]);

    }
}
