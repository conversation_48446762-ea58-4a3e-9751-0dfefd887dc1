<?php

namespace Domain\Chat\Models;

use Carbon\Carbon;
use Database\Factories\AppChatFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Domain\Chat\QueryBuilders\AppChatQueryBuilder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property int $id
 * @property int $from
 * @property int $to
 * @property string $text
 * @property bool $read
 * @property string $type
 * @property Carbon $created_at
 * @method static AppChat|AppChatQueryBuilder query()
 */
class AppChat extends Model {

    use HasFactory, SoftDeletes;

    public const TYPE_TEXT = 'TEXT';
    public const TYPE_INVITE = 'INVITE';
    public const POST_MESSAGE = 'postMessage';

    protected $table = 'messages';

    protected $fillable = ['text', 'to', 'from', 'type', 'read'];

    public function messageReport(): BelongsToMany
    {
        return $this->belongsToMany(config('domain.user'), 'message_report', 'message_id', 'user_id');
    }

    public function newEloquentBuilder($query): Builder
    {
        return new AppChatQueryBuilder($query);
    }

    protected static function newFactory(): AppChatFactory
    {
        return AppChatFactory::new();
    }
}
