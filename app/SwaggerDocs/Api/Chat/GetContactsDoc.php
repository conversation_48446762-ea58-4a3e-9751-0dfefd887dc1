<?php

namespace App\SwaggerDocs\Api\Chat;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class GetContactsDoc extends \OpenApi\Annotations\Get
{
    public function __construct() {
        parent::__construct([
            'path' => '/user/chat/contacts',
            'tags' => ['Chat'],
            'summary' => "Get contacts",
            'operationId' => 'chat-user-contacts',
            "description" => "Here you can get auth user companions",
            'security' => [
                [
                    'bearerAuth' => []
                ]
            ],
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "User Companions",
                    content: new OA\JsonContent(properties: [
                        new OA\Property(property: 'data', type: "array", items: new OA\Items(
                            properties: chatContactPropertiesDoc()
                        ))
                    ])
                ),
                unauthorizedResponseDoc()
            ],
        ]);

    }
}
