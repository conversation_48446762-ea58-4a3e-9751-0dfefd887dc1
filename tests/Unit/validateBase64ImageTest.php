<?php

use Tests\Constants;

use function PHPUnit\Framework\assertFalse;
use function PHPUnit\Framework\assertTrue;

it('empty_value', function () {

    $valid = validateBase64Image();
    assertFalse($valid);

});

it('not_valid_value', function () {

    $valid1 = validateBase64Image("not_valid,sdfsd");
    $valid2 = validateBase64Image("not_valid");

    assertFalse($valid1);
    assertFalse($valid2);

});

it('valid_value', function () {

    $valid = validateBase64Image(Constants::BASE64_VALUE);
    assertTrue($valid);

});

it('not_image_start_with', function () {

    $valid = validateBase64Image("data:not_image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/");
    assertFalse($valid);

});
