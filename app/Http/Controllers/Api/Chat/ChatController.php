<?php

namespace App\Http\Controllers\Api\Chat;

use App\Events\Message\MessageEvent;
use Illuminate\Http\Request;
use Domain\Auth\Models\User;
use Domain\Chat\DTOs\ChatDTO;
use Domain\Chat\Models\AppChat;
use Illuminate\Http\JsonResponse;
use App\Helpers\Response\Response;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Domain\Auth\DTOs\CredentialDTO;
use Domain\Auth\Requests\ContactRequest;
use Domain\Chat\Requests\CreateMessageRequest;
use Domain\Chat\JsonResources\MessageResource;
use Domain\Auth\JsonResources\ContactResource;
use Domain\Chat\Contracts\CreateMessageInterface;
use Domain\Auth\Actions\CompanionActions\AddToAppChatAction;
use Domain\Auth\Actions\CompanionActions\RemoveFromAppChatAction;
use App\Http\Resources\Notification\MessageNotificationResource;
// Swagger
use App\SwaggerDocs\Api\Chat\GetContactsDoc;
use App\SwaggerDocs\Api\Chat\AddContactDoc;
use App\SwaggerDocs\Api\Chat\CreateMessageDoc;
use App\SwaggerDocs\Api\Chat\DeleteContactDoc;
use App\SwaggerDocs\Api\Chat\GetMessagesForDoc;
use App\SwaggerDocs\Api\Chat\HasUnreadMessagesDoc;

class ChatController extends Controller
{

    private $manager;
    public function __construct() {
        $this->manager = app()->make(config('broadcasting.manager'));
    }


    // список собеседников
    #[GetContactsDoc]
    public function getContacts()
    {
        $contacts = AppChat::query()->userContacts();
        return Response::HTTP_OK(ContactResource::collection($contacts));
    }


    // добавить пользователя в чат
    #[AddContactDoc]
    public function add(ContactRequest $request, AddToAppChatAction $action): JsonResponse
    {
        $dto = CredentialDTO::fromRequest($request);
        return Response::HTTP_OK($action($dto));
    }


    // удалить пользователя из чата
    #[DeleteContactDoc]
    public function remove(ContactRequest $request, RemoveFromAppChatAction $action): JsonResponse
    {
        $dto = CredentialDTO::fromRequest($request);
        return Response::HTTP_OK($action($dto));
    }


    // получить все сообщения между авторизованным пользователем и его собеседником
    #[GetMessagesForDoc()]
    public function getMessagesFor(string $contact_id): JsonResponse
    {
        $author = authUser();

        // Делаем сообщения от собеседника прочитанными
        AppChat::query()->makeRead($author?->id, $contact_id);

        $messages = AppChat::query()->getMessagesFor(authUser()?->id, $contact_id)
            ->paginate(config('constants.messages_per_page'));
        return Response::HTTP_OK(MessageResource::collection($messages));
    }


    // отправить сообщение собеседнику
    #[CreateMessageDoc]
    public function createMessage(
        CreateMessageRequest $request, string $_,
        CreateMessageInterface $action): JsonResponse
    {
        $dto = ChatDTO::fromRequest($request);

        /** @var User $companion */
        $companion = User::query()->users($dto->companion_id)->firstOrError();
        $message = DB::transaction(function () use($action, $dto, $companion) {
            $companion->addToChat($dto->author_id);
            return $action($dto);
        });

        $auth = authUser();
        // Event
        event(new MessageEvent($message, $companion, $auth));
        // Centrifugo
        $this->manager->publishMessage($message, $auth, new MessageNotificationResource($message, $auth));

        return Response::HTTP_CREATED(new MessageResource($message));
    }

    // удалить сообщение
    public function removeMessage(Request $request, string $contact_id, string $message_id): JsonResponse
    {
        // $auth = $request->user();
        // $user = findUser($contact_id);
        // if ($user && $auth->id === $user->id) {
        //     $message = findAuthMessage($message_id);
        //     $message->delete();

        //     $last_message = Message::query()->where(function ($query) use ($message) {
        //         $query->where('to', findUser($message->to)->id);
        //         $query->where('from', auth()->id());
        //     })->orderByDesc('created_at')->get()->first();
        //     if (!$last_message) {
        //         //broadcast(new RemoveMessageEvent($message, $message, findUser($message->to), $auth))->toOthers();
        //         return;
        //     }

        //     //broadcast(new RemoveMessageEvent($message, $last_message, findUser($message->to), $auth))->toOthers();
        // } else {
        //     throw new \DomainException(config('constants.check_user'));
        // }
        return Response::HTTP_OK([]);
    }


    // обновить сообщение
    public function updateMessage(CreateMessageRequest $request, string $contact_id, string $message_id): JsonResponse
    {
        // $auth = $request->user();
        // $user = findUser($contact_id);
        // if ($user && $auth->id === $user->id) {
        //     $message = findAuthMessage($message_id);
        //     $message->update(['text' => $request['message']]);

        //     //broadcast(new UpdateMessageEvent($message, findUser($message->to), Auth::user()))->toOthers();
        //     return $message;
        // } else {
        //     throw new \DomainException(config('constants.check_user'));
        // }
        return Response::HTTP_OK([]);
    }


    #[HasUnreadMessagesDoc]
    public function hasUnreadMessages(): JsonResponse {
        $exists = AppChat::query()->hasUnreadMessages(authUser()?->id);
        return Response::HTTP_CREATED(['exists' => $exists]);
    }

}
