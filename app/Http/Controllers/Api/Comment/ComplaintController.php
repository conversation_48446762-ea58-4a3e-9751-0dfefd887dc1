<?php

namespace App\Http\Controllers\Api\Comment;

use App\Helpers\Response\Response;
use App\Http\Controllers\Controller;
use Domain\Comment\Models\BlogComment;
use Domain\Comment\Models\ProjectComment;
use Domain\Complaint\DTOs\ComplaintDTO;
use Domain\Complaint\Enums\CredentialsEnum;
use Domain\Complaint\Actions\AddToComplaintAction;
use Domain\Complaint\Requests\CreateComplaintRequest;
use Domain\Comment\JsonResources\SecondaryCommentResource;
use Domain\Comment\JsonResources\SecondaryBlogCommentResource;
// swagger
use App\SwaggerDocs\Api\Comments\BlogComments\CreateComplainDoc as CreateComplainToBlogCommentDoc;
use App\SwaggerDocs\Api\Comments\ProjectComments\CreateComplainDoc as CreateComplainToProjectCommentDoc;

class ComplaintController extends Controller
{

    #[CreateComplainToProjectCommentDoc]
    public function addProjectCommentToComplaint(CreateComplaintRequest $request, string $id, AddToComplaintAction $action)
    {
        $dto = ComplaintDTO::fromRequest($request);
        $comment = ProjectComment::query()->commentById($id);
        $dto->setAttributes([
            CredentialsEnum::ATTACHABLE->value => $comment->id,
            CredentialsEnum::METHOD->value => 'addToCommentComplaints'
        ]);

        $action($dto);

        return Response::HTTP_CREATED(new SecondaryCommentResource($comment));
    }


    #[CreateComplainToBlogCommentDoc]
    public function addBlogCommentToComplaint(CreateComplaintRequest $request, string $id, AddToComplaintAction $action)
    {
        $dto = ComplaintDTO::fromRequest($request);
        $comment = BlogComment::query()->commentById($id);
        $dto->setAttributes([
            CredentialsEnum::ATTACHABLE->value => $comment->id,
            CredentialsEnum::METHOD->value => 'addToBlogCommentComplaints'
        ]);

        $action($dto);

        return Response::HTTP_CREATED(new SecondaryBlogCommentResource($comment));
    }
}
