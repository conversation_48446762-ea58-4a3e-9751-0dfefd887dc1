<?php

namespace Domain\Blog\Actions;

use Domain\Blog\DTOs\BlogDTO;
use Domain\Blog\Models\Blog;
use Illuminate\Support\Facades\DB;
use Domain\Blog\Models\BlogSubject;
use Domain\Blog\Contracts\UpdateBlogInterface;
use Domain\Blog\Enums\BlogSubjectStatusEnum as Status;

class UpdateBlogAction implements UpdateBlogInterface  {

    public function __invoke(BlogDTO $dto, Blog $blog): Blog {

        /** @var Blog $blog */
        $blog = DB::transaction(function() use($dto, $blog) {
            $blog->update([
                'title' => $dto->title,
                'project_id' => $dto->project_id,
                'description' => $dto->description,
                'user_id' => $dto->project_id ? null : $dto->user_id,
                'status' => $dto->status ? $dto->status : $blog->status,
            ]);

            $blog->subjects()->detach();

            foreach($dto->subjects as $subject) {
                $tag = BlogSubject::query()->where("title", 'like', $subject ?? "")->first();
                if (!$tag) {
                    $tag = BlogSubject::create(['title' => $subject, 'status' => Status::MODERATION]);
                }
                $blog->subjects()->attach($tag);
            }
            return $blog;
        });

        return Blog::query()->where('slug', $blog->slug)->firstOrError();
    }
}
