<?php

namespace Domain\Residence\QueryBuilders;

use Domain\Residence\Models\City;
use Illuminate\Database\Eloquent\Builder;

class CityQueryBuilder extends Builder {

    public function city(string $id = "", string $country_id = "", string $region_id): City|null {
        if (!$region_id) {
            return $this
                ->whereNull('region_id')
                ->where('id', 'like', $id)
                ->where('country_id', 'like', $country_id)
                ->first();
        }
        return $this
            ->where('id', 'like', $id)
            ->where('region_id', 'like', $region_id)
            ->where('country_id', 'like', $country_id)
            ->first();
    }

    public function searchCityNoRegion(string $result = "", string|null $country_id): Builder {
        return $this
            ->where('country_id', 'like', $country_id ?? "")
            ->where('title', 'like', '%' . $result . '%')
            ->whereNull('region_id');
    }
}
