<?php

namespace App\SwaggerDocs\Api\Comments\BlogComments;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class PrimaryCommentsDoc extends \OpenApi\Annotations\Get
{
    public function __construct() {
        parent::__construct([
            'path' => '/blog-comments/{id}',
            'tags' => ['Blog comments'],
            'summary' => "Primary comments",
            'operationId' => 'Primary blog comments',
            "parameters" => [
                new OA\Parameter(name: "id", description: "Blog id", required: true, in: "path",
                    schema: new OA\Schema(type: "string")
                )
            ],
            "description" => "Here you can get Blog Primary comments",
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Primary Comments",
                    content: paginationJsonContentDoc([])
                ),
                badRequestResponseDoc()
            ],
        ]);

    }
}
