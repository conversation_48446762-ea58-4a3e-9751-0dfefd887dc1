<?php

namespace Domain\Project\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DeleteProjectTeamTagRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'project_id' => 'required'
        ];
        if (gettype(request()->tagId) === 'integer') {
            $rules['tagId'] = 'required|integer';
        } else {
            $rules['tagId'] = 'required|string';
        }
        return $rules;
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'project_id' => $this['project'],
        ]);
    }

    public function messages()
    {
        return [
            'tagId.required' => 'Поле tagId обязательно для заполнения.',
            'tagId.string' => 'Поле tagId должно быть строкой или числом',
            'tagId.integer' => 'Поле tagId должно быть строкой или числом',
            'project_id.required' => config('constants.transmit_incorrect_data'),
        ];
    }
}
