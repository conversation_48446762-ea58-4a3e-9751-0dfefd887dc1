<?php

namespace Domain\Auth\DTOs;

use Illuminate\Http\Request;
use Support\Traits\Makeable;
use Domain\Auth\Enums\FeedbackEnum;

class FeedbackDTO {

    use Makeable;

    public function __construct(
        public readonly ?string $text = "",
    ){}

    public static function fromRequest(Request $request): FeedbackDTO {

        return self::make(...$request->only([
            FeedbackEnum::TEXT->value
        ]));
    }
}
