<?php

namespace  Domain\Project\Traits;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;

trait BookmarkTrait {

    public function bookmarks(): BelongsToMany
    {
        return $this->belongsToMany(config('domain.user'), 'user_bookmarks', 'project_id', 'user_id');
    }

    public function hasInBookmarks($user_id): bool
    {
        return $this->bookmarks()->where('user_id', $user_id)->exists();
    }
}
