<?php

namespace Support\DTOs;

use Support\Traits\Makeable;


class EmailDTO {

    use Makeable;

    public function __construct(
        public readonly ?string $id = "",
        public readonly ?string $slug = "",
        public readonly ?string $email = "",
        public readonly ?string $title = "",
        public readonly ?string $text = "",
        public readonly ?string $firstname = "",
        public readonly ?string $verification_code = "",
    ){}

    public static function fromProject($project): EmailDTO {

        return self::make(...[
            'firstname' => $project->user?->firstname,
            'email' => $project->user?->email,
            'text' => $project->reason,
            'title' => $project->title,
            'slug' => $project->slug
        ]);
    }

    public static function fromUser($user): EmailDTO {

        return self::make(...[
            'email' => $user->email,
            'firstname' => $user->firstname,
            'verification_code' => $user->verification_code
        ]);
    }

    public function toArray(): array {
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'text' => $this->text,
            'email' => $this->email,
            'title' => $this->title,
            'firstname' => $this->firstname,
            'verification_code' => $this->verification_code
        ];
    }
}
