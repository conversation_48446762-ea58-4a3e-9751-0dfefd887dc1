sudo php artisan down
sudo git pull http://deploy:<EMAIL>/team2you/team2you.git
sudo composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev
sudo php artisan migrate --force
sudo php artisan cache:clear
sudo php artisan auth:clear-resets
sudo php artisan route:clear
#- sudo php artisan route:cache
sudo php artisan config:clear
sudo php artisan config:cache
sudo npm install
sudo npm run production
