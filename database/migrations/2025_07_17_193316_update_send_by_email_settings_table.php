<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('send_by_email_settings', function (Blueprint $table) {
            $table->boolean('commentAnswer')->default(true)->nullable(false)->change();
            $table->boolean('likeProject')->default(true)->nullable(false)->change();
            $table->boolean('popularProjects')->default(true)->nullable(false)->change();
            $table->boolean('newMessage')->default(true)->nullable(false)->change();
        });
    }

    public function down(): void
    {
        Schema::table('send_by_email_settings', function (Blueprint $table) {
            $table->boolean('commentAnswer')->default(false)->nullable(false)->change();
            $table->boolean('likeProject')->default(false)->nullable(false)->change();
            $table->boolean('popularProjects')->default(false)->nullable(false)->change();
            $table->boolean('newMessage')->default(false)->nullable(false)->change();
        });
    }
};
