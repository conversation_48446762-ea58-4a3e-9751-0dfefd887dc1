<?php

namespace App\SwaggerDocs\Api\Combine;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class CombineDoc extends \OpenApi\Annotations\Get
{
    public function __construct() {
        parent::__construct([
            'path' => '/combine',
            'tags' => ['Combine'],
            'summary' => "Any Active Users and Projects",
            'operationId' => 'combine-users-projects',
            "description" => "Here you can get any active Users and Projects",
            "parameters" => [
                new OA\Parameter(name: "page", description: "Combine page", required: false, in: "query",
                    schema: new OA\Schema(type: "string")
                )
            ],
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Any Active Users and Projects",
                )
            ],
        ]);

    }
}
