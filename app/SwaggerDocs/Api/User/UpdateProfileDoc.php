<?php

namespace App\SwaggerDocs\Api\User;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class UpdateProfileDoc extends \OpenApi\Annotations\Put
{
    public function __construct() {
        parent::__construct([
            'path' => '/profile',
            'tags' => ['Profile'],
            'summary' => "Update user profile",
            'operationId' => 'update-profile',
            "description" => "Update user profile",
            'security' => [
                [
                    'bearerAuth' => []
                ]
            ],
            'requestBody' => new OA\RequestBody(required: true,
                request: "update-profile",
                content: new OA\MediaType(mediaType: "application/json",
                schema: new OA\Schema(
                        properties: [
                            new OA\Property(property: 'bio', description: "User avatar base64 format or null", type: "string"),
                            new OA\Property(property: 'skills', description: "User skills json or null", type: "string",
                                example: '["php", "java"]'
                            ),
                            new OA\Property(property: 'qualities', description: "User qualities json or null", type: "string",
                                example: '["php", "qwerty"]'
                            )
                        ]
            ))),
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Success",
                    content: new OA\JsonContent(properties: [
                        new OA\Property(property: "data", type: "object",
                            properties: userDetailPropertiesDoc()
                        )
                    ])
                ),
                badRequestResponseDoc(),
                unauthorizedResponseDoc(),
                unprocessableEntityResponseDoc()
            ],
        ]);

    }
}
