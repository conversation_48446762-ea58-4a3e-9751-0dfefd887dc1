<?php

namespace App\SwaggerDocs\Api\Chat;

use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;

#[\Attribute(\Attribute::TARGET_CLASS | \Attribute::TARGET_METHOD | \Attribute::IS_REPEATABLE)]
class HasUnreadMessagesDoc extends \OpenApi\Annotations\Get
{
    public function __construct() {
        parent::__construct([
            'path' => '/user/chat/has-unread-messages',
            'tags' => ['Chat'],
            'summary' => "Does the user have unread messages?",
            'operationId' => 'has-unread-messages',
            "description" => "Does the user have unread messages?",
            'security' => [
                [
                    'bearerAuth' => []
                ]
            ],
            'responses' => [
                new OA\Response(
                    response: Response::HTTP_OK,
                    description: "Successfully created",
                    content: new OA\JsonContent(properties: [
                        new OA\Property(property: 'exists', type: "boolean")])
                ),
                unauthorizedResponseDoc()
            ],
        ]);

    }
}
