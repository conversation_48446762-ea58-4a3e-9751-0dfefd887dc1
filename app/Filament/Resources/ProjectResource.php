<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProjectResource\Pages;
use Domain\Project\Models\Project;
use Domain\Project\Enums\ProjectStatusEnum;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProjectResource extends Resource
{
    protected static ?string $model = Project::class;

    protected static ?string $navigationIcon = 'heroicon-o-folder';

    protected static ?string $navigationGroup = 'Проект';
    protected static ?string $modelLabel = 'Проект';

    protected static ?string $pluralModelLabel = 'Проекты';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Название')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Select::make('status')
                    ->label('Статус')
                    ->options([
                        ProjectStatusEnum::DRAFT->value => 'Черновик',
                        ProjectStatusEnum::MODERATION->value => 'На модерации',
                        ProjectStatusEnum::REJECTED->value => 'Отклонён',
                        ProjectStatusEnum::ACTIVE->value => 'Активный',
                        ProjectStatusEnum::CLOSED->value => 'Закрыт',
                        ProjectStatusEnum::DELETED->value => 'Удалён',
                    ])
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function (callable $set, $state) {
                        // Очистка поля reason если статус не "Отклонён"
                        if ($state !== ProjectStatusEnum::REJECTED->value) {
                            $set('reason', null);
                        }
                    }),

                Forms\Components\Textarea::make('reason')
                    ->label('Причина отклонения')
                    ->rows(3)
                    ->columnSpanFull()
                    ->required(fn (Forms\Get $get): bool => $get('status') === ProjectStatusEnum::REJECTED->value)
                    ->visible(fn (Forms\Get $get): bool => $get('status') === ProjectStatusEnum::REJECTED->value)
                    ->placeholder('Укажите причину отклонения проекта')
                    ->helperText('Это поле обязательно для заполнения при отклонении проекта'),

                Forms\Components\Select::make('user_id')
                    ->label('Автор')
                    ->relationship('user', 'firstname')
                    ->searchable()
                    ->preload()
                    ->required(),

                Forms\Components\Textarea::make('description')
                    ->label('Описание')
                    ->rows(3)
                    ->columnSpanFull(),

                Forms\Components\Textarea::make('about')
                    ->label('О проекте')
                    ->rows(5)
                    ->columnSpanFull(),

                Forms\Components\Select::make('tags')
                    ->label('Теги')
                    ->relationship('tags', 'title')
                    ->multiple()
                    ->searchable()
                    ->preload()
                    ->placeholder('Выберите теги для проекта')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Название')
                    ->searchable()
                    ->sortable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Статус')
                    ->colors([
                        'gray' => ProjectStatusEnum::DRAFT->value,
                        'success' => ProjectStatusEnum::MODERATION->value,
                        'secondary' => ProjectStatusEnum::REJECTED->value,
                        'primary' => ProjectStatusEnum::ACTIVE->value,
                        'warning' => ProjectStatusEnum::CLOSED->value,
                        'danger' => ProjectStatusEnum::DELETED->value,
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        ProjectStatusEnum::DRAFT->value => 'Черновик',
                        ProjectStatusEnum::MODERATION->value => 'На модерации',
                        ProjectStatusEnum::REJECTED->value => 'Отклонён',
                        ProjectStatusEnum::ACTIVE->value => 'Активный',
                        ProjectStatusEnum::CLOSED->value => 'Закрыт',
                        ProjectStatusEnum::DELETED->value => 'Удалён',
                        default => $state,
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Автор')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('reason')
                    ->label('Причина отклонения')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (!$state || strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->placeholder('—')
                    ->visible(fn ($record): bool => $record && $record->status === ProjectStatusEnum::REJECTED->value),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Дата создания')
                    ->dateTime('d.m.Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Обновлён')
                    ->dateTime('d.m.Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('deleted_at')
                    ->label('Удалён')
                    ->dateTime('d.m.Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Статус')
                    ->options([
                        ProjectStatusEnum::DRAFT->value => 'Черновик',
                        ProjectStatusEnum::MODERATION->value => 'На модерации',
                        ProjectStatusEnum::REJECTED->value => 'Отклонён',
                        ProjectStatusEnum::ACTIVE->value => 'Активный',
                        ProjectStatusEnum::CLOSED->value => 'Закрыт',
                        ProjectStatusEnum::DELETED->value => 'Удалён',
                    ]),

                Tables\Filters\SelectFilter::make('tags')
                    ->label('Теги')
                    ->relationship('tags', 'title')
                    ->multiple()
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('created_at')
                    ->label('Дата создания')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('С'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('По'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),

                Tables\Filters\TrashedFilter::make()
                    ->label('Удалённые записи'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('Просмотр'),
                Tables\Actions\EditAction::make()
                    ->label('Редактировать'),

                // Quick action для быстрого одобрения
                Tables\Actions\Action::make('approve')
                    ->label('Одобрить')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn ($record): bool => $record && $record->status === ProjectStatusEnum::MODERATION->value)
                    ->requiresConfirmation()
                    ->modalHeading('Одобрить проект?')
                    ->modalDescription('Проект будет переведён в статус "Активный"')
                    ->action(function ($record) {
                        $record->update([
                            'status' => ProjectStatusEnum::ACTIVE->value,
                            'reason' => null, // Очистка причины отклонения если была
                        ]);
                    }),

                // Quick action для быстрого отклонения
                Tables\Actions\Action::make('reject')
                    ->label('Отклонить')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn ($record): bool => $record && $record->status === ProjectStatusEnum::MODERATION->value)
                    ->form([
                        Forms\Components\Textarea::make('reason')
                            ->label('Причина отклонения')
                            ->required()
                            ->rows(3)
                            ->placeholder('Укажите причину отклонения проекта'),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update([
                            'status' => ProjectStatusEnum::REJECTED->value,
                            'reason' => $data['reason'],
                        ]);
                    }),

                Tables\Actions\DeleteAction::make()
                    ->label('Удалить'),
                Tables\Actions\RestoreAction::make()
                    ->label('Восстановить'),
                Tables\Actions\ForceDeleteAction::make()
                    ->label('Удалить навсегда'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Удалить выбранные'),
                    Tables\Actions\RestoreBulkAction::make()
                        ->label('Восстановить выбранные'),
                    Tables\Actions\ForceDeleteBulkAction::make()
                        ->label('Удалить навсегда'),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->poll('30s');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProjects::route('/'),
            'create' => Pages\CreateProject::route('/create'),
            'edit' => Pages\EditProject::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
